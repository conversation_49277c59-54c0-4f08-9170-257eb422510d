import 'dart:convert';
import 'package:http/http.dart' as http;
import '../utils/constants.dart';
import 'storage_service.dart';

class ApiService {
  static final ApiService _instance = ApiService._internal();
  factory ApiService() => _instance;
  ApiService._internal();

  final StorageService _storageService = StorageService();

  Future<Map<String, String>> _getHeaders({bool requiresAuth = true}) async {
    final headers = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    };

    if (requiresAuth) {
      final token = await _storageService.getToken();
      if (token != null) {
        headers['Authorization'] = 'Bearer $token';
      }
    }

    return headers;
  }

  Future<Map<String, dynamic>> _handleResponse(http.Response response) async {
    final responseBody = json.decode(response.body);

    if (response.statusCode >= 200 && response.statusCode < 300) {
      return responseBody;
    } else {
      throw ApiException(
        statusCode: response.statusCode,
        message: responseBody['message'] ?? 'An error occurred',
        errors: responseBody['errors'],
      );
    }
  }

  // Authentication endpoints
  Future<Map<String, dynamic>> register({
    required String name,
    required String email,
    required String password,
    required String passwordConfirmation,
    String? phone,
  }) async {
    final response = await http.post(
      Uri.parse('${Constants.apiBaseUrl}/register'),
      headers: await _getHeaders(requiresAuth: false),
      body: json.encode({
        'name': name,
        'email': email,
        'password': password,
        'password_confirmation': passwordConfirmation,
        'phone': phone,
      }),
    );

    return _handleResponse(response);
  }

  Future<Map<String, dynamic>> login({
    required String email,
    required String password,
  }) async {
    final response = await http.post(
      Uri.parse('${Constants.apiBaseUrl}/login'),
      headers: await _getHeaders(requiresAuth: false),
      body: json.encode({
        'email': email,
        'password': password,
      }),
    );

    return _handleResponse(response);
  }

  Future<Map<String, dynamic>> logout() async {
    final response = await http.post(
      Uri.parse('${Constants.apiBaseUrl}/logout'),
      headers: await _getHeaders(),
    );

    return _handleResponse(response);
  }

  Future<Map<String, dynamic>> getProfile() async {
    final response = await http.get(
      Uri.parse('${Constants.apiBaseUrl}/profile'),
      headers: await _getHeaders(),
    );

    return _handleResponse(response);
  }

  // Scooter endpoints
  Future<Map<String, dynamic>> getScooters({
    double? latitude,
    double? longitude,
    double? radius,
  }) async {
    var url = '${Constants.apiBaseUrl}/scooters';
    final queryParams = <String, String>{};

    if (latitude != null) queryParams['latitude'] = latitude.toString();
    if (longitude != null) queryParams['longitude'] = longitude.toString();
    if (radius != null) queryParams['radius'] = radius.toString();

    if (queryParams.isNotEmpty) {
      url += '?' + queryParams.entries.map((e) => '${e.key}=${e.value}').join('&');
    }

    final response = await http.get(
      Uri.parse(url),
      headers: await _getHeaders(),
    );

    return _handleResponse(response);
  }

  Future<Map<String, dynamic>> getScooter(int scooterId) async {
    final response = await http.get(
      Uri.parse('${Constants.apiBaseUrl}/scooters/$scooterId'),
      headers: await _getHeaders(),
    );

    return _handleResponse(response);
  }

  // Ride endpoints
  Future<Map<String, dynamic>> startRide({
    required int scooterId,
    required double latitude,
    required double longitude,
  }) async {
    final response = await http.post(
      Uri.parse('${Constants.apiBaseUrl}/rides'),
      headers: await _getHeaders(),
      body: json.encode({
        'scooter_id': scooterId,
        'start_latitude': latitude,
        'start_longitude': longitude,
      }),
    );

    return _handleResponse(response);
  }

  Future<Map<String, dynamic>> endRide({
    required int rideId,
    required double latitude,
    required double longitude,
  }) async {
    final response = await http.put(
      Uri.parse('${Constants.apiBaseUrl}/rides/$rideId/end'),
      headers: await _getHeaders(),
      body: json.encode({
        'end_latitude': latitude,
        'end_longitude': longitude,
      }),
    );

    return _handleResponse(response);
  }

  Future<Map<String, dynamic>> getCurrentRide() async {
    final response = await http.get(
      Uri.parse('${Constants.apiBaseUrl}/rides/current'),
      headers: await _getHeaders(),
    );

    return _handleResponse(response);
  }

  Future<Map<String, dynamic>> getRideHistory({
    int page = 1,
    int perPage = 20,
  }) async {
    final response = await http.get(
      Uri.parse('${Constants.apiBaseUrl}/rides?page=$page&per_page=$perPage'),
      headers: await _getHeaders(),
    );

    return _handleResponse(response);
  }

  Future<Map<String, dynamic>> getRide(int rideId) async {
    final response = await http.get(
      Uri.parse('${Constants.apiBaseUrl}/rides/$rideId'),
      headers: await _getHeaders(),
    );

    return _handleResponse(response);
  }
}

class ApiException implements Exception {
  final int statusCode;
  final String message;
  final Map<String, dynamic>? errors;

  ApiException({
    required this.statusCode,
    required this.message,
    this.errors,
  });

  @override
  String toString() {
    return 'ApiException: $message (Status: $statusCode)';
  }

  String get userFriendlyMessage {
    switch (statusCode) {
      case 401:
        return 'Please log in to continue';
      case 403:
        return 'You don\'t have permission to perform this action';
      case 404:
        return 'The requested resource was not found';
      case 422:
        return 'Please check your input and try again';
      case 500:
        return 'Server error. Please try again later';
      default:
        return message;
    }
  }
}
