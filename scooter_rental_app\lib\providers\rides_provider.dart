import 'package:flutter/foundation.dart';
import '../models/ride.dart';
import '../models/scooter.dart';
import '../services/api_service.dart';
import '../services/location_service.dart';

class RidesProvider with ChangeNotifier {
  final ApiService _apiService = ApiService();
  final LocationService _locationService = LocationService();

  Ride? _currentRide;
  List<Ride> _rideHistory = [];
  bool _isLoading = false;
  String? _errorMessage;

  Ride? get currentRide => _currentRide;
  List<Ride> get rideHistory => _rideHistory;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  bool get hasActiveRide => _currentRide != null && _currentRide!.isActive;

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String? error) {
    _errorMessage = error;
    notifyListeners();
  }

  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  Future<bool> startRide({
    required Scooter scooter,
    double? latitude,
    double? longitude,
  }) async {
    _setLoading(true);
    _setError(null);

    try {
      // Get current location if not provided
      double? lat = latitude;
      double? lng = longitude;

      if (lat == null || lng == null) {
        final locationResult = await _locationService.getCurrentLocation();
        if (locationResult.isSuccess && locationResult.position != null) {
          lat = locationResult.position!.latitude;
          lng = locationResult.position!.longitude;
        } else {
          _setError('Unable to get current location');
          return false;
        }
      }

      final response = await _apiService.startRide(
        scooterId: scooter.id,
        latitude: lat!,
        longitude: lng!,
      );

      _currentRide = Ride.fromJson(response['ride']);
      _setError(null);
      return true;
    } catch (e) {
      _setError('Failed to start ride. Please try again.');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> endRide({
    double? latitude,
    double? longitude,
  }) async {
    if (_currentRide == null) {
      _setError('No active ride to end');
      return false;
    }

    _setLoading(true);
    _setError(null);

    try {
      // Get current location if not provided
      double? lat = latitude;
      double? lng = longitude;

      if (lat == null || lng == null) {
        final locationResult = await _locationService.getCurrentLocation();
        if (locationResult.isSuccess && locationResult.position != null) {
          lat = locationResult.position!.latitude;
          lng = locationResult.position!.longitude;
        } else {
          _setError('Unable to get current location');
          return false;
        }
      }

      final response = await _apiService.endRide(
        rideId: _currentRide!.id,
        latitude: lat!,
        longitude: lng!,
      );

      final completedRide = Ride.fromJson(response['ride']);

      // Add to history and clear current ride
      _rideHistory.insert(0, completedRide);
      _currentRide = null;

      _setError(null);
      return true;
    } catch (e) {
      _setError('Failed to end ride. Please try again.');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> loadCurrentRide() async {
    _setLoading(true);
    _setError(null);

    try {
      final response = await _apiService.getCurrentRide();

      if (response['ride'] != null) {
        _currentRide = Ride.fromJson(response['ride']);
      } else {
        _currentRide = null;
      }

      _setError(null);
      return true;
    } catch (e) {
      _setError('Failed to load current ride');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> loadRideHistory({
    int page = 1,
    int perPage = 20,
    bool append = false,
  }) async {
    if (!append) {
      _setLoading(true);
    }
    _setError(null);

    try {
      final response = await _apiService.getRideHistory(
        page: page,
        perPage: perPage,
      );

      final ridesList = response['rides'] as List;
      final rides = ridesList.map((json) => Ride.fromJson(json)).toList();

      if (append) {
        _rideHistory.addAll(rides);
      } else {
        _rideHistory = rides;
      }

      _setError(null);
      return true;
    } catch (e) {
      _setError('Failed to load ride history');
      return false;
    } finally {
      if (!append) {
        _setLoading(false);
      }
    }
  }

  Future<Ride?> getRideDetails(int rideId) async {
    try {
      final response = await _apiService.getRide(rideId);
      return Ride.fromJson(response['ride']);
    } catch (e) {
      _setError('Failed to get ride details');
      return null;
    }
  }

  double? getCurrentRideDistance() {
    if (_currentRide == null) return null;

    final currentLocation = _locationService.currentPosition;
    if (currentLocation == null) return null;

    return _locationService.calculateDistance(
      startLatitude: _currentRide!.startLatitude,
      startLongitude: _currentRide!.startLongitude,
      endLatitude: currentLocation.latitude,
      endLongitude: currentLocation.longitude,
    );
  }

  String? getFormattedCurrentRideDistance() {
    final distance = getCurrentRideDistance();
    if (distance == null) return null;

    return _locationService.formatDistance(distance);
  }

  Duration? getCurrentRideDuration() {
    if (_currentRide == null) return null;

    return DateTime.now().difference(_currentRide!.startTime);
  }

  String getFormattedCurrentRideDuration() {
    final duration = getCurrentRideDuration();
    if (duration == null) return '0m';

    final hours = duration.inHours;
    final minutes = duration.inMinutes % 60;

    if (hours > 0) {
      return '${hours}h ${minutes}m';
    }
    return '${minutes}m';
  }

  double? estimateCurrentRideCost(double pricePerMinute) {
    final duration = getCurrentRideDuration();
    if (duration == null) return null;

    final minutes = duration.inMinutes;
    return minutes * pricePerMinute;
  }

  List<Ride> getCompletedRides() {
    return _rideHistory.where((ride) => ride.isCompleted).toList();
  }

  List<Ride> getCancelledRides() {
    return _rideHistory.where((ride) => ride.isCancelled).toList();
  }

  double getTotalSpent() {
    return _rideHistory
        .where((ride) => ride.isCompleted && ride.totalCost != null)
        .fold(0.0, (sum, ride) => sum + ride.totalCost!);
  }

  int getTotalRides() {
    return _rideHistory.where((ride) => ride.isCompleted).length;
  }

  Duration getTotalRideTime() {
    final totalMinutes = _rideHistory
        .where((ride) => ride.isCompleted && ride.durationMinutes != null)
        .fold(0, (sum, ride) => sum + ride.durationMinutes!);

    return Duration(minutes: totalMinutes);
  }

  void clearCurrentRide() {
    _currentRide = null;
    notifyListeners();
  }

  void refreshRideHistory() {
    loadRideHistory();
  }
}
