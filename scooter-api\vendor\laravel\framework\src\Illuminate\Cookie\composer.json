{"name": "illuminate/cookie", "description": "The Illuminate Cookie package.", "license": "MIT", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "require": {"php": "^8.2", "ext-hash": "*", "illuminate/collections": "^12.0", "illuminate/contracts": "^12.0", "illuminate/macroable": "^12.0", "illuminate/support": "^12.0", "symfony/http-foundation": "^7.2.0", "symfony/http-kernel": "^7.2.0"}, "autoload": {"psr-4": {"Illuminate\\Cookie\\": ""}}, "extra": {"branch-alias": {"dev-master": "12.x-dev"}}, "config": {"sort-packages": true}, "minimum-stability": "dev"}