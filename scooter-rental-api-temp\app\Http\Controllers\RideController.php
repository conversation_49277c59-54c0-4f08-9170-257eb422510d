<?php

namespace App\Http\Controllers;

use App\Models\Ride;
use App\Models\Scooter;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;

class RideController extends Controller
{
    /**
     * Start a new ride.
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'scooter_id' => 'required|exists:scooters,id',
            'start_latitude' => 'required|numeric|between:-90,90',
            'start_longitude' => 'required|numeric|between:-180,180',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], Response::HTTP_UNPROCESSABLE_ENTITY);
        }

        $user = $request->user();
        $scooter = Scooter::findOrFail($request->scooter_id);

        // Check if user already has an active ride
        if ($user->hasActiveRide()) {
            return response()->json([
                'message' => 'You already have an active ride',
            ], Response::HTTP_UNPROCESSABLE_ENTITY);
        }

        // Check if scooter is available
        if (!$scooter->isAvailable()) {
            return response()->json([
                'message' => 'Scooter is not available',
            ], Response::HTTP_UNPROCESSABLE_ENTITY);
        }

        // Check battery level
        if ($scooter->battery_level < 20) {
            return response()->json([
                'message' => 'Scooter battery is too low',
            ], Response::HTTP_UNPROCESSABLE_ENTITY);
        }

        DB::beginTransaction();

        try {
            // Create the ride
            $ride = Ride::create([
                'user_id' => $user->id,
                'scooter_id' => $scooter->id,
                'start_latitude' => $request->start_latitude,
                'start_longitude' => $request->start_longitude,
                'start_time' => now(),
                'status' => 'active',
            ]);

            // Mark scooter as in use
            $scooter->markAsInUse();

            DB::commit();

            // Load relationships
            $ride->load(['user', 'scooter']);

            return response()->json([
                'message' => 'Ride started successfully',
                'ride' => $ride,
            ], Response::HTTP_CREATED);

        } catch (\Exception $e) {
            DB::rollBack();
            
            return response()->json([
                'message' => 'Failed to start ride',
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * End a ride.
     */
    public function end(Request $request, Ride $ride)
    {
        $validator = Validator::make($request->all(), [
            'end_latitude' => 'required|numeric|between:-90,90',
            'end_longitude' => 'required|numeric|between:-180,180',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], Response::HTTP_UNPROCESSABLE_ENTITY);
        }

        $user = $request->user();

        // Check if user owns this ride
        if ($ride->user_id !== $user->id) {
            return response()->json([
                'message' => 'Unauthorized',
            ], Response::HTTP_FORBIDDEN);
        }

        // Check if ride is active
        if (!$ride->isActive()) {
            return response()->json([
                'message' => 'Ride is not active',
            ], Response::HTTP_UNPROCESSABLE_ENTITY);
        }

        DB::beginTransaction();

        try {
            // End the ride
            $ride->endRide($request->end_latitude, $request->end_longitude);

            // Update scooter location
            $ride->scooter->update([
                'latitude' => $request->end_latitude,
                'longitude' => $request->end_longitude,
            ]);

            DB::commit();

            // Load relationships
            $ride->load(['user', 'scooter']);

            return response()->json([
                'message' => 'Ride ended successfully',
                'ride' => $ride,
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            
            return response()->json([
                'message' => 'Failed to end ride',
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Cancel a ride.
     */
    public function cancel(Request $request, Ride $ride)
    {
        $user = $request->user();

        // Check if user owns this ride
        if ($ride->user_id !== $user->id) {
            return response()->json([
                'message' => 'Unauthorized',
            ], Response::HTTP_FORBIDDEN);
        }

        // Check if ride is active
        if (!$ride->isActive()) {
            return response()->json([
                'message' => 'Ride is not active',
            ], Response::HTTP_UNPROCESSABLE_ENTITY);
        }

        DB::beginTransaction();

        try {
            // Cancel the ride
            $ride->cancelRide();

            DB::commit();

            // Load relationships
            $ride->load(['user', 'scooter']);

            return response()->json([
                'message' => 'Ride cancelled successfully',
                'ride' => $ride,
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            
            return response()->json([
                'message' => 'Failed to cancel ride',
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Get current active ride for user.
     */
    public function current(Request $request)
    {
        $user = $request->user();
        $ride = $user->currentRide;

        if (!$ride) {
            return response()->json([
                'ride' => null,
            ]);
        }

        // Load relationships
        $ride->load(['scooter']);

        // Add current duration and estimated cost
        $ride->current_duration_minutes = $ride->getCurrentDurationMinutes();
        $ride->current_estimated_cost = $ride->getCurrentEstimatedCost();

        return response()->json([
            'ride' => $ride,
        ]);
    }

    /**
     * Get ride history for user.
     */
    public function index(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'page' => 'nullable|integer|min:1',
            'per_page' => 'nullable|integer|min:1|max:100',
            'status' => 'nullable|in:active,completed,cancelled',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], Response::HTTP_UNPROCESSABLE_ENTITY);
        }

        $user = $request->user();
        $perPage = $request->get('per_page', 20);

        $query = $user->rides()->with('scooter')->latest();

        // Filter by status if provided
        if ($request->has('status')) {
            $query->where('status', $request->status);
        }

        $rides = $query->paginate($perPage);

        return response()->json([
            'rides' => $rides->items(),
            'pagination' => [
                'current_page' => $rides->currentPage(),
                'last_page' => $rides->lastPage(),
                'per_page' => $rides->perPage(),
                'total' => $rides->total(),
            ],
        ]);
    }

    /**
     * Get a specific ride.
     */
    public function show(Request $request, Ride $ride)
    {
        $user = $request->user();

        // Check if user owns this ride
        if ($ride->user_id !== $user->id) {
            return response()->json([
                'message' => 'Unauthorized',
            ], Response::HTTP_FORBIDDEN);
        }

        // Load relationships
        $ride->load(['scooter']);

        // Add additional information
        if ($ride->isActive()) {
            $ride->current_duration_minutes = $ride->getCurrentDurationMinutes();
            $ride->current_estimated_cost = $ride->getCurrentEstimatedCost();
        }

        $ride->distance_traveled = $ride->getFormattedDistanceTraveled();
        $ride->formatted_duration = $ride->getFormattedDuration();

        return response()->json([
            'ride' => $ride,
        ]);
    }

    /**
     * Get ride statistics for user.
     */
    public function statistics(Request $request)
    {
        $user = $request->user();

        $stats = [
            'total_rides' => $user->total_rides,
            'total_spent' => $user->total_spent,
            'total_ride_time_minutes' => $user->total_ride_time,
            'average_ride_cost' => $user->total_rides > 0 ? round($user->total_spent / $user->total_rides, 2) : 0,
            'average_ride_duration' => $user->total_rides > 0 ? round($user->total_ride_time / $user->total_rides, 1) : 0,
        ];

        return response()->json([
            'statistics' => $stats,
        ]);
    }
}
