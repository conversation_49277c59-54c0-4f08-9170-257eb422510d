<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Scooter extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'code',
        'latitude',
        'longitude',
        'battery_level',
        'status',
        'price_per_minute',
        'model',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'latitude' => 'decimal:8',
        'longitude' => 'decimal:8',
        'battery_level' => 'integer',
        'price_per_minute' => 'decimal:2',
    ];

    /**
     * The possible status values for a scooter.
     */
    const STATUS_AVAILABLE = 'available';
    const STATUS_IN_USE = 'in_use';
    const STATUS_MAINTENANCE = 'maintenance';
    const STATUS_OFFLINE = 'offline';

    /**
     * Get all possible status values.
     */
    public static function getStatuses()
    {
        return [
            self::STATUS_AVAILABLE,
            self::STATUS_IN_USE,
            self::STATUS_MAINTENANCE,
            self::STATUS_OFFLINE,
        ];
    }

    /**
     * Get the rides for the scooter.
     */
    public function rides()
    {
        return $this->hasMany(Ride::class);
    }

    /**
     * Get the current active ride for the scooter.
     */
    public function currentRide()
    {
        return $this->hasOne(Ride::class)->where('status', 'active');
    }

    /**
     * Check if scooter is available for rent.
     */
    public function isAvailable()
    {
        return $this->status === self::STATUS_AVAILABLE && $this->battery_level >= 20;
    }

    /**
     * Check if scooter is currently in use.
     */
    public function isInUse()
    {
        return $this->status === self::STATUS_IN_USE;
    }

    /**
     * Mark scooter as in use.
     */
    public function markAsInUse()
    {
        $this->update(['status' => self::STATUS_IN_USE]);
    }

    /**
     * Mark scooter as available.
     */
    public function markAsAvailable()
    {
        $this->update(['status' => self::STATUS_AVAILABLE]);
    }

    /**
     * Mark scooter as maintenance.
     */
    public function markAsMaintenance()
    {
        $this->update(['status' => self::STATUS_MAINTENANCE]);
    }

    /**
     * Mark scooter as offline.
     */
    public function markAsOffline()
    {
        $this->update(['status' => self::STATUS_OFFLINE]);
    }

    /**
     * Scope to get only available scooters.
     */
    public function scopeAvailable($query)
    {
        return $query->where('status', self::STATUS_AVAILABLE)
                    ->where('battery_level', '>=', 20);
    }

    /**
     * Scope to get scooters within a radius of given coordinates.
     */
    public function scopeWithinRadius($query, $latitude, $longitude, $radius = 2000)
    {
        $haversine = "(6371000 * acos(cos(radians(?)) 
                     * cos(radians(latitude)) 
                     * cos(radians(longitude) - radians(?)) 
                     + sin(radians(?)) 
                     * sin(radians(latitude))))";
        
        return $query->selectRaw("*, {$haversine} AS distance", [$latitude, $longitude, $latitude])
                    ->whereRaw("{$haversine} < ?", [$latitude, $longitude, $latitude, $radius])
                    ->orderBy('distance');
    }

    /**
     * Calculate distance to given coordinates.
     */
    public function distanceTo($latitude, $longitude)
    {
        $earthRadius = 6371000; // Earth's radius in meters

        $latFrom = deg2rad($this->latitude);
        $lonFrom = deg2rad($this->longitude);
        $latTo = deg2rad($latitude);
        $lonTo = deg2rad($longitude);

        $latDelta = $latTo - $latFrom;
        $lonDelta = $lonTo - $lonFrom;

        $a = sin($latDelta / 2) * sin($latDelta / 2) +
             cos($latFrom) * cos($latTo) *
             sin($lonDelta / 2) * sin($lonDelta / 2);
        $c = 2 * atan2(sqrt($a), sqrt(1 - $a));

        return $earthRadius * $c;
    }

    /**
     * Get formatted distance to given coordinates.
     */
    public function getFormattedDistanceTo($latitude, $longitude)
    {
        $distance = $this->distanceTo($latitude, $longitude);
        
        if ($distance < 1000) {
            return round($distance) . 'm';
        } else {
            return round($distance / 1000, 1) . 'km';
        }
    }

    /**
     * Get total rides count for this scooter.
     */
    public function getTotalRidesAttribute()
    {
        return $this->rides()->where('status', 'completed')->count();
    }

    /**
     * Get total revenue generated by this scooter.
     */
    public function getTotalRevenueAttribute()
    {
        return $this->rides()->where('status', 'completed')->sum('total_cost');
    }
}
