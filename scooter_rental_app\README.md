# ScooterRide - Flutter App

A modern Flutter application for electric scooter rentals with real-time tracking, QR code scanning, and seamless ride management.

## Features

- **User Authentication**: Secure login and registration
- **Scooter Discovery**: Find nearby available scooters
- **QR Code Scanning**: Quick scooter unlock via QR codes
- **Real-time Tracking**: Live ride monitoring and location tracking
- **Ride Management**: Start, track, and end rides seamlessly
- **Ride History**: View past rides and statistics
- **Payment Integration**: Secure payment processing
- **Profile Management**: User profile and settings

## Screenshots

*Screenshots would go here in a real project*

## Getting Started

### Prerequisites

- Flutter SDK (>=3.0.0)
- Dart SDK
- Android Studio / VS Code
- Android device or emulator
- iOS device or simulator (for iOS development)

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd scooter_rental_app
   ```

2. **Install dependencies**
   ```bash
   flutter pub get
   ```

3. **Configure API endpoint**
   - Update the `apiBaseUrl` in `lib/utils/constants.dart`
   - Set it to your Laravel API URL (e.g., `http://10.0.2.2:8000/api` for Android emulator)

4. **Configure Google Maps (Optional)**
   - Get a Google Maps API key from Google Cloud Console
   - Add the API key to your platform-specific configuration files

5. **Run the app**
   ```bash
   flutter run
   ```

## Project Structure

```
lib/
├── main.dart                 # App entry point
├── models/                   # Data models
│   ├── user.dart
│   ├── scooter.dart
│   └── ride.dart
├── screens/                  # UI screens
│   ├── splash_screen.dart
│   ├── auth/
│   │   ├── login_screen.dart
│   │   └── register_screen.dart
│   ├── home_screen.dart
│   ├── qr_scanner_screen.dart
│   ├── ride_screen.dart
│   ├── ride_history_screen.dart
│   └── profile_drawer.dart
├── services/                 # Business logic services
│   ├── api_service.dart
│   ├── auth_service.dart
│   ├── location_service.dart
│   └── storage_service.dart
├── providers/                # State management
│   ├── auth_provider.dart
│   ├── scooters_provider.dart
│   └── rides_provider.dart
├── widgets/                  # Reusable UI components
│   ├── custom_button.dart
│   ├── scooter_marker.dart
│   └── loading_indicator.dart
└── utils/                    # Utilities and constants
    ├── constants.dart
    └── routes.dart
```

## Dependencies

### Main Dependencies
- `provider`: State management
- `http`: HTTP client for API calls
- `geolocator`: Location services
- `qr_code_scanner`: QR code scanning
- `google_maps_flutter`: Maps integration
- `flutter_secure_storage`: Secure local storage
- `permission_handler`: Runtime permissions

### Dev Dependencies
- `flutter_test`: Testing framework
- `flutter_lints`: Linting rules

## Configuration

### API Configuration
Update the API base URL in `lib/utils/constants.dart`:
```dart
static const String apiBaseUrl = 'http://your-api-url.com/api';
```

### Permissions
The app requires the following permissions:
- Location access (for finding nearby scooters)
- Camera access (for QR code scanning)

These are automatically requested when needed.

## State Management

The app uses the Provider pattern for state management with three main providers:

- **AuthProvider**: Manages user authentication state
- **ScootersProvider**: Handles scooter data and location
- **RidesProvider**: Manages ride state and history

## API Integration

The app communicates with a Laravel backend API. Key endpoints include:

- `POST /auth/login` - User login
- `POST /auth/register` - User registration
- `GET /scooters` - Get available scooters
- `POST /rides` - Start a ride
- `PUT /rides/{id}/end` - End a ride
- `GET /rides` - Get ride history

## Testing

Run tests using:
```bash
flutter test
```

## Building for Production

### Android
```bash
flutter build apk --release
# or for app bundle
flutter build appbundle --release
```

### iOS
```bash
flutter build ios --release
```

## Troubleshooting

### Common Issues

1. **API Connection Issues**
   - Ensure the API URL is correct
   - Check network connectivity
   - Verify the Laravel API is running

2. **Location Permission Denied**
   - Grant location permissions in device settings
   - Ensure location services are enabled

3. **QR Scanner Not Working**
   - Grant camera permissions
   - Test on a physical device (camera doesn't work in emulator)

### Debug Mode
Run in debug mode for detailed error logs:
```bash
flutter run --debug
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support and questions:
- Create an issue in the repository
- Contact the development team

## Roadmap

- [ ] Push notifications for ride updates
- [ ] In-app payments integration
- [ ] Social features (ride sharing)
- [ ] Advanced analytics dashboard
- [ ] Multi-language support
- [ ] Dark mode theme
- [ ] Offline mode capabilities
