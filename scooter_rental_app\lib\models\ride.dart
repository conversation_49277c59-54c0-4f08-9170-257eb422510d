enum RideStatus { active, completed, cancelled }

class Ride {
  final int id;
  final int userId;
  final int scooterId;
  final double startLatitude;
  final double startLongitude;
  final double? endLatitude;
  final double? endLongitude;
  final DateTime startTime;
  final DateTime? endTime;
  final double? totalCost;
  final int? durationMinutes;
  final RideStatus status;
  final DateTime createdAt;
  final DateTime updatedAt;

  // Optional related objects
  final String? scooterCode;
  final String? userName;

  Ride({
    required this.id,
    required this.userId,
    required this.scooterId,
    required this.startLatitude,
    required this.startLongitude,
    this.endLatitude,
    this.endLongitude,
    required this.startTime,
    this.endTime,
    this.totalCost,
    this.durationMinutes,
    required this.status,
    required this.createdAt,
    required this.updatedAt,
    this.scooterCode,
    this.userName,
  });

  factory Ride.fromJson(Map<String, dynamic> json) {
    return Ride(
      id: json['id'],
      userId: json['user_id'],
      scooterId: json['scooter_id'],
      startLatitude: double.parse(json['start_latitude'].toString()),
      startLongitude: double.parse(json['start_longitude'].toString()),
      endLatitude: json['end_latitude'] != null
          ? double.parse(json['end_latitude'].toString())
          : null,
      endLongitude: json['end_longitude'] != null
          ? double.parse(json['end_longitude'].toString())
          : null,
      startTime: DateTime.parse(json['start_time']),
      endTime: json['end_time'] != null ? DateTime.parse(json['end_time']) : null,
      totalCost: json['total_cost'] != null
          ? double.parse(json['total_cost'].toString())
          : null,
      durationMinutes: json['duration_minutes'],
      status: _statusFromString(json['status']),
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
      scooterCode: json['scooter_code'],
      userName: json['user_name'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'scooter_id': scooterId,
      'start_latitude': startLatitude,
      'start_longitude': startLongitude,
      'end_latitude': endLatitude,
      'end_longitude': endLongitude,
      'start_time': startTime.toIso8601String(),
      'end_time': endTime?.toIso8601String(),
      'total_cost': totalCost,
      'duration_minutes': durationMinutes,
      'status': _statusToString(status),
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'scooter_code': scooterCode,
      'user_name': userName,
    };
  }

  static RideStatus _statusFromString(String status) {
    switch (status.toLowerCase()) {
      case 'active':
        return RideStatus.active;
      case 'completed':
        return RideStatus.completed;
      case 'cancelled':
        return RideStatus.cancelled;
      default:
        return RideStatus.cancelled;
    }
  }

  static String _statusToString(RideStatus status) {
    switch (status) {
      case RideStatus.active:
        return 'active';
      case RideStatus.completed:
        return 'completed';
      case RideStatus.cancelled:
        return 'cancelled';
    }
  }

  Ride copyWith({
    int? id,
    int? userId,
    int? scooterId,
    double? startLatitude,
    double? startLongitude,
    double? endLatitude,
    double? endLongitude,
    DateTime? startTime,
    DateTime? endTime,
    double? totalCost,
    int? durationMinutes,
    RideStatus? status,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? scooterCode,
    String? userName,
  }) {
    return Ride(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      scooterId: scooterId ?? this.scooterId,
      startLatitude: startLatitude ?? this.startLatitude,
      startLongitude: startLongitude ?? this.startLongitude,
      endLatitude: endLatitude ?? this.endLatitude,
      endLongitude: endLongitude ?? this.endLongitude,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      totalCost: totalCost ?? this.totalCost,
      durationMinutes: durationMinutes ?? this.durationMinutes,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      scooterCode: scooterCode ?? this.scooterCode,
      userName: userName ?? this.userName,
    );
  }

  bool get isActive => status == RideStatus.active;
  bool get isCompleted => status == RideStatus.completed;
  bool get isCancelled => status == RideStatus.cancelled;

  String get statusDisplayName {
    switch (status) {
      case RideStatus.active:
        return 'Active';
      case RideStatus.completed:
        return 'Completed';
      case RideStatus.cancelled:
        return 'Cancelled';
    }
  }

  Duration? get duration {
    if (endTime != null) {
      return endTime!.difference(startTime);
    }
    return null;
  }

  String get formattedDuration {
    if (durationMinutes != null) {
      final hours = durationMinutes! ~/ 60;
      final minutes = durationMinutes! % 60;
      if (hours > 0) {
        return '${hours}h ${minutes}m';
      }
      return '${minutes}m';
    }
    return 'N/A';
  }

  @override
  String toString() {
    return 'Ride{id: $id, scooter: $scooterCode, status: $status, duration: $formattedDuration}';
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Ride && runtimeType == other.runtimeType && id == other.id;

  @override
  int get hashCode => id.hashCode;
}
