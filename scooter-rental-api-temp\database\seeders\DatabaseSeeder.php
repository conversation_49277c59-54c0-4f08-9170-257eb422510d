<?php

namespace Database\Seeders;

// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use App\Models\User;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Create admin user
        User::create([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'password' => Hash::make('admin123'),
            'phone' => '+1234567890',
            'email_verified_at' => now(),
        ]);

        // Create test users
        User::create([
            'name' => '<PERSON>',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'phone' => '+1234567891',
            'email_verified_at' => now(),
        ]);

        User::create([
            'name' => '<PERSON>',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'phone' => '+1234567892',
            'email_verified_at' => now(),
        ]);

        User::create([
            'name' => '<PERSON>',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'phone' => '+1234567893',
            'email_verified_at' => now(),
        ]);

        $this->command->info('Created ' . User::count() . ' users');

        // Seed scooters
        $this->call([
            ScooterSeeder::class,
        ]);
    }
}
