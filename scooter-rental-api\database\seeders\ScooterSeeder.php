<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Scooter;

class ScooterSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Sample locations around a city center (using San Francisco coordinates as example)
        $locations = [
            ['lat' => 37.7749, 'lng' => -122.4194], // Downtown SF
            ['lat' => 37.7849, 'lng' => -122.4094], // North Beach
            ['lat' => 37.7649, 'lng' => -122.4294], // Mission District
            ['lat' => 37.7949, 'lng' => -122.3994], // Financial District
            ['lat' => 37.7549, 'lng' => -122.4394], // Castro
            ['lat' => 37.7749, 'lng' => -122.3894], // SOMA
            ['lat' => 37.7849, 'lng' => -122.4294], // Chinatown
            ['lat' => 37.7649, 'lng' => -122.3994], // Potrero Hill
            ['lat' => 37.7949, 'lng' => -122.4194], // Russian Hill
            ['lat' => 37.7549, 'lng' => -122.4094], // Noe Valley
            ['lat' => 37.7749, 'lng' => -122.4494], // Richmond
            ['lat' => 37.7849, 'lng' => -122.3794], // SOMA East
            ['lat' => 37.7649, 'lng' => -122.4494], // Sunset
            ['lat' => 37.7949, 'lng' => -122.3894], // Embarcadero
            ['lat' => 37.7549, 'lng' => -122.4294], // Mission Bay
        ];

        $statuses = ['available', 'available', 'available', 'available', 'in_use', 'maintenance'];
        $models = ['ScootX Pro', 'EcoRide 2000', 'UrbanGlide', 'CityZoom', 'GreenWheel'];

        foreach ($locations as $index => $location) {
            // Create multiple scooters per location
            for ($i = 1; $i <= 3; $i++) {
                $scooterNumber = ($index * 3) + $i;
                
                Scooter::create([
                    'code' => 'SC' . str_pad($scooterNumber, 4, '0', STR_PAD_LEFT),
                    'latitude' => $location['lat'] + (rand(-50, 50) / 10000), // Add small random offset
                    'longitude' => $location['lng'] + (rand(-50, 50) / 10000),
                    'battery_level' => rand(20, 100),
                    'status' => $statuses[array_rand($statuses)],
                    'price_per_minute' => round(rand(20, 35) / 100, 2), // $0.20 - $0.35 per minute
                    'model' => $models[array_rand($models)],
                ]);
            }
        }

        // Create some additional scooters with specific scenarios
        
        // Low battery scooters
        for ($i = 1; $i <= 5; $i++) {
            Scooter::create([
                'code' => 'LB' . str_pad($i, 3, '0', STR_PAD_LEFT),
                'latitude' => 37.7749 + (rand(-100, 100) / 10000),
                'longitude' => -122.4194 + (rand(-100, 100) / 10000),
                'battery_level' => rand(5, 19), // Low battery
                'status' => 'offline',
                'price_per_minute' => 0.25,
                'model' => $models[array_rand($models)],
            ]);
        }

        // Maintenance scooters
        for ($i = 1; $i <= 3; $i++) {
            Scooter::create([
                'code' => 'MT' . str_pad($i, 3, '0', STR_PAD_LEFT),
                'latitude' => 37.7749 + (rand(-100, 100) / 10000),
                'longitude' => -122.4194 + (rand(-100, 100) / 10000),
                'battery_level' => rand(50, 100),
                'status' => 'maintenance',
                'price_per_minute' => 0.25,
                'model' => $models[array_rand($models)],
            ]);
        }

        $this->command->info('Created ' . Scooter::count() . ' scooters');
    }
}
