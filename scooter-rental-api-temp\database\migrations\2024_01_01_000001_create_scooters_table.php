<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('scooters', function (Blueprint $table) {
            $table->id();
            $table->string('code')->unique();
            $table->decimal('latitude', 10, 8);
            $table->decimal('longitude', 11, 8);
            $table->integer('battery_level')->default(100);
            $table->enum('status', ['available', 'in_use', 'maintenance', 'offline'])->default('available');
            $table->decimal('price_per_minute', 8, 2)->default(0.25);
            $table->string('model')->nullable();
            $table->timestamps();

            // Indexes for better performance
            $table->index(['latitude', 'longitude']);
            $table->index('status');
            $table->index('battery_level');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('scooters');
    }
};
