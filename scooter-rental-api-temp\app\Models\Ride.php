<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class Ride extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'scooter_id',
        'start_latitude',
        'start_longitude',
        'end_latitude',
        'end_longitude',
        'start_time',
        'end_time',
        'total_cost',
        'duration_minutes',
        'status',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'start_latitude' => 'decimal:8',
        'start_longitude' => 'decimal:8',
        'end_latitude' => 'decimal:8',
        'end_longitude' => 'decimal:8',
        'start_time' => 'datetime',
        'end_time' => 'datetime',
        'total_cost' => 'decimal:2',
        'duration_minutes' => 'integer',
    ];

    /**
     * The possible status values for a ride.
     */
    const STATUS_ACTIVE = 'active';
    const STATUS_COMPLETED = 'completed';
    const STATUS_CANCELLED = 'cancelled';

    /**
     * Get all possible status values.
     */
    public static function getStatuses()
    {
        return [
            self::STATUS_ACTIVE,
            self::STATUS_COMPLETED,
            self::STATUS_CANCELLED,
        ];
    }

    /**
     * Get the user that owns the ride.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the scooter that was used for the ride.
     */
    public function scooter()
    {
        return $this->belongsTo(Scooter::class);
    }

    /**
     * Check if ride is active.
     */
    public function isActive()
    {
        return $this->status === self::STATUS_ACTIVE;
    }

    /**
     * Check if ride is completed.
     */
    public function isCompleted()
    {
        return $this->status === self::STATUS_COMPLETED;
    }

    /**
     * Check if ride is cancelled.
     */
    public function isCancelled()
    {
        return $this->status === self::STATUS_CANCELLED;
    }

    /**
     * End the ride.
     */
    public function endRide($endLatitude, $endLongitude)
    {
        $endTime = now();
        $durationMinutes = $this->start_time->diffInMinutes($endTime);
        $totalCost = $durationMinutes * $this->scooter->price_per_minute;

        $this->update([
            'end_latitude' => $endLatitude,
            'end_longitude' => $endLongitude,
            'end_time' => $endTime,
            'duration_minutes' => $durationMinutes,
            'total_cost' => $totalCost,
            'status' => self::STATUS_COMPLETED,
        ]);

        // Mark scooter as available
        $this->scooter->markAsAvailable();

        return $this;
    }

    /**
     * Cancel the ride.
     */
    public function cancelRide()
    {
        $this->update([
            'status' => self::STATUS_CANCELLED,
            'end_time' => now(),
        ]);

        // Mark scooter as available
        $this->scooter->markAsAvailable();

        return $this;
    }

    /**
     * Get the current duration of the ride in minutes.
     */
    public function getCurrentDurationMinutes()
    {
        if ($this->isActive()) {
            return $this->start_time->diffInMinutes(now());
        }
        
        return $this->duration_minutes ?? 0;
    }

    /**
     * Get the current estimated cost of the ride.
     */
    public function getCurrentEstimatedCost()
    {
        if ($this->isActive()) {
            $currentDuration = $this->getCurrentDurationMinutes();
            return $currentDuration * $this->scooter->price_per_minute;
        }
        
        return $this->total_cost ?? 0;
    }

    /**
     * Calculate distance traveled during the ride.
     */
    public function getDistanceTraveled()
    {
        if (!$this->end_latitude || !$this->end_longitude) {
            return 0;
        }

        $earthRadius = 6371000; // Earth's radius in meters

        $latFrom = deg2rad($this->start_latitude);
        $lonFrom = deg2rad($this->start_longitude);
        $latTo = deg2rad($this->end_latitude);
        $lonTo = deg2rad($this->end_longitude);

        $latDelta = $latTo - $latFrom;
        $lonDelta = $lonTo - $lonFrom;

        $a = sin($latDelta / 2) * sin($latDelta / 2) +
             cos($latFrom) * cos($latTo) *
             sin($lonDelta / 2) * sin($lonDelta / 2);
        $c = 2 * atan2(sqrt($a), sqrt(1 - $a));

        return $earthRadius * $c;
    }

    /**
     * Get formatted distance traveled.
     */
    public function getFormattedDistanceTraveled()
    {
        $distance = $this->getDistanceTraveled();
        
        if ($distance < 1000) {
            return round($distance) . 'm';
        } else {
            return round($distance / 1000, 1) . 'km';
        }
    }

    /**
     * Get formatted duration.
     */
    public function getFormattedDuration()
    {
        $minutes = $this->duration_minutes ?? $this->getCurrentDurationMinutes();
        $hours = intval($minutes / 60);
        $remainingMinutes = $minutes % 60;
        
        if ($hours > 0) {
            return $hours . 'h ' . $remainingMinutes . 'm';
        }
        
        return $remainingMinutes . 'm';
    }

    /**
     * Scope to get only active rides.
     */
    public function scopeActive($query)
    {
        return $query->where('status', self::STATUS_ACTIVE);
    }

    /**
     * Scope to get only completed rides.
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', self::STATUS_COMPLETED);
    }

    /**
     * Scope to get rides for a specific user.
     */
    public function scopeForUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Scope to get recent rides.
     */
    public function scopeRecent($query, $days = 30)
    {
        return $query->where('created_at', '>=', now()->subDays($days));
    }
}
