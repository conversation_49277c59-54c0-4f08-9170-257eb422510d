import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:flutter/foundation.dart';

class StorageService {
  static final StorageService _instance = StorageService._internal();
  factory StorageService() => _instance;
  StorageService._internal();

  static const FlutterSecureStorage _storage = FlutterSecureStorage(
    aOptions: AndroidOptions(
      encryptedSharedPreferences: true,
    ),
    iOptions: IOSOptions(),
    webOptions: WebOptions(
      dbName: 'ScooterRideSecureStorage',
      publicKey: 'ScooterRidePublicKey',
    ),
  );

  // Keys for storage
  static const String _tokenKey = 'auth_token';
  static const String _userIdKey = 'user_id';
  static const String _userEmailKey = 'user_email';
  static const String _rememberMeKey = 'remember_me';
  static const String _onboardingCompletedKey = 'onboarding_completed';
  static const String _lastLocationKey = 'last_location';

  // Token management
  Future<void> saveToken(String token) async {
    await _storage.write(key: _tokenKey, value: token);
  }

  Future<String?> getToken() async {
    return await _storage.read(key: _tokenKey);
  }

  Future<void> deleteToken() async {
    await _storage.delete(key: _tokenKey);
  }

  Future<bool> hasToken() async {
    final token = await getToken();
    return token != null && token.isNotEmpty;
  }

  // User data management
  Future<void> saveUserId(int userId) async {
    await _storage.write(key: _userIdKey, value: userId.toString());
  }

  Future<int?> getUserId() async {
    final userIdString = await _storage.read(key: _userIdKey);
    return userIdString != null ? int.tryParse(userIdString) : null;
  }

  Future<void> saveUserEmail(String email) async {
    await _storage.write(key: _userEmailKey, value: email);
  }

  Future<String?> getUserEmail() async {
    return await _storage.read(key: _userEmailKey);
  }

  // Remember me functionality
  Future<void> setRememberMe(bool remember) async {
    await _storage.write(key: _rememberMeKey, value: remember.toString());
  }

  Future<bool> getRememberMe() async {
    final rememberString = await _storage.read(key: _rememberMeKey);
    return rememberString == 'true';
  }

  // Onboarding status
  Future<void> setOnboardingCompleted(bool completed) async {
    await _storage.write(
        key: _onboardingCompletedKey, value: completed.toString());
  }

  Future<bool> isOnboardingCompleted() async {
    final completedString = await _storage.read(key: _onboardingCompletedKey);
    return completedString == 'true';
  }

  // Location data
  Future<void> saveLastLocation(double latitude, double longitude) async {
    await _storage.write(key: _lastLocationKey, value: '$latitude,$longitude');
  }

  Future<Map<String, double>?> getLastLocation() async {
    final locationString = await _storage.read(key: _lastLocationKey);
    if (locationString != null) {
      final parts = locationString.split(',');
      if (parts.length == 2) {
        final latitude = double.tryParse(parts[0]);
        final longitude = double.tryParse(parts[1]);
        if (latitude != null && longitude != null) {
          return {'latitude': latitude, 'longitude': longitude};
        }
      }
    }
    return null;
  }

  // Generic storage methods
  Future<void> saveString(String key, String value) async {
    await _storage.write(key: key, value: value);
  }

  Future<String?> getString(String key) async {
    return await _storage.read(key: key);
  }

  Future<void> saveBool(String key, bool value) async {
    await _storage.write(key: key, value: value.toString());
  }

  Future<bool> getBool(String key, {bool defaultValue = false}) async {
    final value = await _storage.read(key: key);
    return value == 'true' ? true : defaultValue;
  }

  Future<void> saveInt(String key, int value) async {
    await _storage.write(key: key, value: value.toString());
  }

  Future<int?> getInt(String key) async {
    final value = await _storage.read(key: key);
    return value != null ? int.tryParse(value) : null;
  }

  Future<void> saveDouble(String key, double value) async {
    await _storage.write(key: key, value: value.toString());
  }

  Future<double?> getDouble(String key) async {
    final value = await _storage.read(key: key);
    return value != null ? double.tryParse(value) : null;
  }

  Future<void> deleteKey(String key) async {
    await _storage.delete(key: key);
  }

  Future<bool> containsKey(String key) async {
    return await _storage.containsKey(key: key);
  }

  // Clear all data
  Future<void> clearAll() async {
    await _storage.deleteAll();
  }

  // Clear user-specific data (keep app settings)
  Future<void> clearUserData() async {
    await deleteToken();
    await _storage.delete(key: _userIdKey);
    await _storage.delete(key: _userEmailKey);
    await _storage.delete(key: _rememberMeKey);
    await _storage.delete(key: _lastLocationKey);
  }

  // Get all stored keys (for debugging)
  Future<Map<String, String>> getAllData() async {
    return await _storage.readAll();
  }
}
