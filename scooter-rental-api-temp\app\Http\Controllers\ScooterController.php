<?php

namespace App\Http\Controllers;

use App\Models\Scooter;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Validator;

class ScooterController extends Controller
{
    /**
     * Get all scooters with optional filtering.
     */
    public function index(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'latitude' => 'nullable|numeric|between:-90,90',
            'longitude' => 'nullable|numeric|between:-180,180',
            'radius' => 'nullable|numeric|min:100|max:50000',
            'status' => 'nullable|in:available,in_use,maintenance,offline',
            'min_battery' => 'nullable|integer|min:0|max:100',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], Response::HTTP_UNPROCESSABLE_ENTITY);
        }

        $query = Scooter::query();

        // Filter by location if provided
        if ($request->has('latitude') && $request->has('longitude')) {
            $radius = $request->get('radius', 2000); // Default 2km radius
            $query->withinRadius(
                $request->latitude,
                $request->longitude,
                $radius
            );
        }

        // Filter by status
        if ($request->has('status')) {
            $query->where('status', $request->status);
        } else {
            // Default to available scooters only
            $query->available();
        }

        // Filter by minimum battery level
        if ($request->has('min_battery')) {
            $query->where('battery_level', '>=', $request->min_battery);
        }

        $scooters = $query->get();

        // Add distance information if location provided
        if ($request->has('latitude') && $request->has('longitude')) {
            $scooters->each(function ($scooter) use ($request) {
                $scooter->distance_formatted = $scooter->getFormattedDistanceTo(
                    $request->latitude,
                    $request->longitude
                );
            });
        }

        return response()->json([
            'scooters' => $scooters,
            'count' => $scooters->count(),
        ]);
    }

    /**
     * Get a specific scooter.
     */
    public function show(Request $request, Scooter $scooter)
    {
        // Add distance if location provided
        if ($request->has('latitude') && $request->has('longitude')) {
            $validator = Validator::make($request->all(), [
                'latitude' => 'numeric|between:-90,90',
                'longitude' => 'numeric|between:-180,180',
            ]);

            if (!$validator->fails()) {
                $scooter->distance_formatted = $scooter->getFormattedDistanceTo(
                    $request->latitude,
                    $request->longitude
                );
            }
        }

        // Load recent rides for this scooter (for admin purposes)
        $scooter->load(['rides' => function ($query) {
            $query->latest()->limit(5);
        }]);

        return response()->json([
            'scooter' => $scooter,
        ]);
    }

    /**
     * Get scooters by QR code.
     */
    public function getByCode(Request $request, $code)
    {
        $scooter = Scooter::where('code', $code)->first();

        if (!$scooter) {
            return response()->json([
                'message' => 'Scooter not found',
            ], Response::HTTP_NOT_FOUND);
        }

        // Check if scooter is available
        if (!$scooter->isAvailable()) {
            return response()->json([
                'message' => 'Scooter is not available',
                'scooter' => $scooter,
            ], Response::HTTP_UNPROCESSABLE_ENTITY);
        }

        return response()->json([
            'scooter' => $scooter,
        ]);
    }

    /**
     * Update scooter location (for admin or maintenance).
     */
    public function updateLocation(Request $request, Scooter $scooter)
    {
        $validator = Validator::make($request->all(), [
            'latitude' => 'required|numeric|between:-90,90',
            'longitude' => 'required|numeric|between:-180,180',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], Response::HTTP_UNPROCESSABLE_ENTITY);
        }

        $scooter->update([
            'latitude' => $request->latitude,
            'longitude' => $request->longitude,
        ]);

        return response()->json([
            'message' => 'Scooter location updated successfully',
            'scooter' => $scooter,
        ]);
    }

    /**
     * Update scooter status (for admin or maintenance).
     */
    public function updateStatus(Request $request, Scooter $scooter)
    {
        $validator = Validator::make($request->all(), [
            'status' => 'required|in:available,in_use,maintenance,offline',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], Response::HTTP_UNPROCESSABLE_ENTITY);
        }

        // Don't allow changing status to in_use if scooter has active ride
        if ($request->status === 'in_use' && !$scooter->currentRide) {
            return response()->json([
                'message' => 'Cannot set status to in_use without active ride',
            ], Response::HTTP_UNPROCESSABLE_ENTITY);
        }

        $scooter->update(['status' => $request->status]);

        return response()->json([
            'message' => 'Scooter status updated successfully',
            'scooter' => $scooter,
        ]);
    }

    /**
     * Update scooter battery level (for maintenance).
     */
    public function updateBattery(Request $request, Scooter $scooter)
    {
        $validator = Validator::make($request->all(), [
            'battery_level' => 'required|integer|min:0|max:100',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], Response::HTTP_UNPROCESSABLE_ENTITY);
        }

        $scooter->update(['battery_level' => $request->battery_level]);

        // If battery is charged and scooter was offline due to low battery, make it available
        if ($request->battery_level >= 20 && $scooter->status === 'offline') {
            $scooter->markAsAvailable();
        }

        return response()->json([
            'message' => 'Scooter battery level updated successfully',
            'scooter' => $scooter,
        ]);
    }

    /**
     * Get scooter statistics.
     */
    public function statistics()
    {
        $stats = [
            'total_scooters' => Scooter::count(),
            'available_scooters' => Scooter::available()->count(),
            'in_use_scooters' => Scooter::where('status', 'in_use')->count(),
            'maintenance_scooters' => Scooter::where('status', 'maintenance')->count(),
            'offline_scooters' => Scooter::where('status', 'offline')->count(),
            'low_battery_scooters' => Scooter::where('battery_level', '<', 20)->count(),
            'average_battery_level' => round(Scooter::avg('battery_level'), 1),
        ];

        return response()->json([
            'statistics' => $stats,
        ]);
    }
}
