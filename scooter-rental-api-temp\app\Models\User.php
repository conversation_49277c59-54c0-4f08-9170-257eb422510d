<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;

class User extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'phone',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'password' => 'hashed',
    ];

    /**
     * Get the rides for the user.
     */
    public function rides()
    {
        return $this->hasMany(Ride::class);
    }

    /**
     * Get the current active ride for the user.
     */
    public function currentRide()
    {
        return $this->hasOne(Ride::class)->where('status', 'active');
    }

    /**
     * Get the completed rides for the user.
     */
    public function completedRides()
    {
        return $this->hasMany(Ride::class)->where('status', 'completed');
    }

    /**
     * Check if user has an active ride.
     */
    public function hasActiveRide()
    {
        return $this->currentRide()->exists();
    }

    /**
     * Get total amount spent by user.
     */
    public function getTotalSpentAttribute()
    {
        return $this->completedRides()->sum('total_cost');
    }

    /**
     * Get total rides count.
     */
    public function getTotalRidesAttribute()
    {
        return $this->completedRides()->count();
    }

    /**
     * Get total ride time in minutes.
     */
    public function getTotalRideTimeAttribute()
    {
        return $this->completedRides()->sum('duration_minutes');
    }
}
