# Scooter Rental API

A robust Laravel-based REST API for electric scooter rental services with user authentication, scooter management, and ride tracking capabilities.

## Features

- **User Authentication**: JWT-based authentication with Laravel Sanctum
- **Scooter Management**: CRUD operations for scooter fleet
- **Ride Management**: Start, track, and end rides
- **Location Services**: GPS-based scooter discovery
- **Real-time Updates**: Live ride tracking and status updates
- **Admin Dashboard**: Fleet management and analytics
- **Payment Integration**: Ready for payment gateway integration

## Requirements

- PHP >= 8.1
- Composer
- MySQL >= 5.7 or PostgreSQL >= 10
- Laravel >= 10.x

## Installation

### 1. Clone the Repository
```bash
git clone <repository-url>
cd scooter-rental-api
```

### 2. Install Dependencies
```bash
composer install
```

### 3. Environment Configuration
```bash
cp .env.example .env
```

Edit the `.env` file with your configuration:
```env
APP_NAME="Scooter Rental API"
APP_URL=http://localhost:8000

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=scooter_rental
DB_USERNAME=your_username
DB_PASSWORD=your_password

# Add other configurations as needed
```

### 4. Generate Application Key
```bash
php artisan key:generate
```

### 5. Database Setup
```bash
# Create database
mysql -u root -p
CREATE DATABASE scooter_rental;
exit

# Run migrations
php artisan migrate

# Seed the database with sample data
php artisan db:seed
```

### 6. Install Laravel Sanctum
```bash
php artisan vendor:publish --provider="Laravel\Sanctum\SanctumServiceProvider"
```

### 7. Start the Development Server
```bash
php artisan serve
```

The API will be available at `http://localhost:8000`

## API Documentation

### Authentication Endpoints

#### Register User
```http
POST /api/auth/register
Content-Type: application/json

{
    "name": "John Doe",
    "email": "<EMAIL>",
    "password": "password123",
    "password_confirmation": "password123",
    "phone": "+**********"
}
```

#### Login User
```http
POST /api/auth/login
Content-Type: application/json

{
    "email": "<EMAIL>",
    "password": "password123"
}
```

#### Logout User
```http
POST /api/auth/logout
Authorization: Bearer {token}
```

### Scooter Endpoints

#### Get Available Scooters
```http
GET /api/scooters?latitude=37.7749&longitude=-122.4194&radius=2000
```

#### Get Scooter by ID
```http
GET /api/scooters/{id}
```

#### Get Scooter by QR Code
```http
GET /api/scooters/code/{code}
```

### Ride Endpoints

#### Start a Ride
```http
POST /api/rides
Authorization: Bearer {token}
Content-Type: application/json

{
    "scooter_id": 1,
    "start_latitude": 37.7749,
    "start_longitude": -122.4194
}
```

#### End a Ride
```http
PUT /api/rides/{id}/end
Authorization: Bearer {token}
Content-Type: application/json

{
    "end_latitude": 37.7849,
    "end_longitude": -122.4094
}
```

#### Get Current Ride
```http
GET /api/rides/current
Authorization: Bearer {token}
```

#### Get Ride History
```http
GET /api/rides?page=1&per_page=20
Authorization: Bearer {token}
```

## Database Schema

### Users Table
- `id` - Primary key
- `name` - User's full name
- `email` - Unique email address
- `password` - Hashed password
- `phone` - Phone number (optional)
- `email_verified_at` - Email verification timestamp
- `created_at`, `updated_at` - Timestamps

### Scooters Table
- `id` - Primary key
- `code` - Unique scooter identifier
- `latitude`, `longitude` - GPS coordinates
- `battery_level` - Battery percentage (0-100)
- `status` - Enum: available, in_use, maintenance, offline
- `price_per_minute` - Rental price per minute
- `model` - Scooter model name
- `created_at`, `updated_at` - Timestamps

### Rides Table
- `id` - Primary key
- `user_id` - Foreign key to users
- `scooter_id` - Foreign key to scooters
- `start_latitude`, `start_longitude` - Start location
- `end_latitude`, `end_longitude` - End location (nullable)
- `start_time` - Ride start timestamp
- `end_time` - Ride end timestamp (nullable)
- `total_cost` - Final ride cost (nullable)
- `duration_minutes` - Ride duration (nullable)
- `status` - Enum: active, completed, cancelled
- `created_at`, `updated_at` - Timestamps

## Project Structure

```
app/
├── Http/
│   ├── Controllers/
│   │   ├── AuthController.php
│   │   ├── ScooterController.php
│   │   ├── RideController.php
│   │   └── AdminController.php
│   └── Middleware/
├── Models/
│   ├── User.php
│   ├── Scooter.php
│   └── Ride.php
database/
├── migrations/
├── seeders/
│   ├── DatabaseSeeder.php
│   └── ScooterSeeder.php
routes/
├── api.php
└── web.php
```

## Testing

Run the test suite:
```bash
php artisan test
```

Create new tests:
```bash
php artisan make:test ScooterTest
```

## Deployment

### Production Setup

1. **Environment Configuration**
   ```bash
   APP_ENV=production
   APP_DEBUG=false
   ```

2. **Optimize for Production**
   ```bash
   composer install --optimize-autoloader --no-dev
   php artisan config:cache
   php artisan route:cache
   php artisan view:cache
   ```

3. **Database Migration**
   ```bash
   php artisan migrate --force
   ```

### Docker Deployment

Create a `Dockerfile`:
```dockerfile
FROM php:8.1-fpm

# Install dependencies and PHP extensions
RUN apt-get update && apt-get install -y \
    git \
    curl \
    libpng-dev \
    libonig-dev \
    libxml2-dev \
    zip \
    unzip

# Install PHP extensions
RUN docker-php-ext-install pdo_mysql mbstring exif pcntl bcmath gd

# Install Composer
COPY --from=composer:latest /usr/bin/composer /usr/bin/composer

# Set working directory
WORKDIR /var/www

# Copy application files
COPY . .

# Install dependencies
RUN composer install

# Set permissions
RUN chown -R www-data:www-data /var/www
```

## Security

- All API endpoints use HTTPS in production
- Passwords are hashed using bcrypt
- API authentication via Laravel Sanctum tokens
- Input validation on all endpoints
- CORS configuration for frontend integration
- Rate limiting on authentication endpoints

## Monitoring and Logging

- Laravel's built-in logging system
- Database query logging in development
- Error tracking and reporting
- Performance monitoring

## Contributing

1. Fork the repository
2. Create a feature branch
3. Write tests for new features
4. Ensure all tests pass
5. Submit a pull request

## API Rate Limiting

- Authentication endpoints: 5 requests per minute
- General API endpoints: 60 requests per minute
- Configurable via middleware

## Support

For issues and questions:
- Create an issue in the repository
- Check the Laravel documentation
- Review the API documentation

## License

This project is licensed under the MIT License.

## Changelog

### Version 1.0.0
- Initial release
- User authentication system
- Scooter management
- Ride tracking
- Basic admin functionality
