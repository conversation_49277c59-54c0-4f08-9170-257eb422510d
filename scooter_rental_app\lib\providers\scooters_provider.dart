import 'package:flutter/foundation.dart';
import 'package:geolocator/geolocator.dart';
import '../models/scooter.dart';
import '../services/api_service.dart';
import '../services/location_service.dart';

class ScootersProvider with ChangeNotifier {
  final ApiService _apiService = ApiService();
  final LocationService _locationService = LocationService();

  List<Scooter> _scooters = [];
  Scooter? _selectedScooter;
  bool _isLoading = false;
  String? _errorMessage;
  Position? _currentLocation;

  List<Scooter> get scooters => _scooters;
  List<Scooter> get availableScooters => 
      _scooters.where((scooter) => scooter.isAvailable).toList();
  Scooter? get selectedScooter => _selectedScooter;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  Position? get currentLocation => _currentLocation;

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String? error) {
    _errorMessage = error;
    notifyListeners();
  }

  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  void selectScooter(Scooter? scooter) {
    _selectedScooter = scooter;
    notifyListeners();
  }

  Future<bool> loadScooters({
    double? latitude,
    double? longitude,
    double? radius,
  }) async {
    _setLoading(true);
    _setError(null);

    try {
      // Use provided coordinates or get current location
      double? lat = latitude;
      double? lng = longitude;

      if (lat == null || lng == null) {
        final locationResult = await _locationService.getCurrentLocation();
        if (locationResult.isSuccess && locationResult.position != null) {
          lat = locationResult.position!.latitude;
          lng = locationResult.position!.longitude;
          _currentLocation = locationResult.position;
        }
      }

      final response = await _apiService.getScooters(
        latitude: lat,
        longitude: lng,
        radius: radius,
      );

      final scootersList = response['scooters'] as List;
      _scooters = scootersList.map((json) => Scooter.fromJson(json)).toList();

      // Sort scooters by distance if location is available
      if (lat != null && lng != null) {
        _scooters.sort((a, b) {
          final distanceA = _locationService.calculateDistance(
            startLatitude: lat!,
            startLongitude: lng!,
            endLatitude: a.latitude,
            endLongitude: a.longitude,
          );
          final distanceB = _locationService.calculateDistance(
            startLatitude: lat,
            startLongitude: lng,
            endLatitude: b.latitude,
            endLongitude: b.longitude,
          );
          return distanceA.compareTo(distanceB);
        });
      }

      _setError(null);
      return true;
    } catch (e) {
      _setError('Failed to load scooters. Please try again.');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> refreshScooters() async {
    return await loadScooters(
      latitude: _currentLocation?.latitude,
      longitude: _currentLocation?.longitude,
    );
  }

  Future<Scooter?> getScooterDetails(int scooterId) async {
    try {
      final response = await _apiService.getScooter(scooterId);
      final scooter = Scooter.fromJson(response['scooter']);
      
      // Update the scooter in the list if it exists
      final index = _scooters.indexWhere((s) => s.id == scooterId);
      if (index != -1) {
        _scooters[index] = scooter;
        notifyListeners();
      }
      
      return scooter;
    } catch (e) {
      _setError('Failed to get scooter details');
      return null;
    }
  }

  List<Scooter> getScootersNearLocation({
    required double latitude,
    required double longitude,
    double radiusInMeters = 1000,
  }) {
    return _scooters.where((scooter) {
      return _locationService.isWithinRadius(
        centerLat: latitude,
        centerLng: longitude,
        targetLat: scooter.latitude,
        targetLng: scooter.longitude,
        radiusInMeters: radiusInMeters,
      );
    }).toList();
  }

  double? getDistanceToScooter(Scooter scooter) {
    if (_currentLocation == null) return null;
    
    return _locationService.calculateDistance(
      startLatitude: _currentLocation!.latitude,
      startLongitude: _currentLocation!.longitude,
      endLatitude: scooter.latitude,
      endLongitude: scooter.longitude,
    );
  }

  String? getFormattedDistanceToScooter(Scooter scooter) {
    final distance = getDistanceToScooter(scooter);
    if (distance == null) return null;
    
    return _locationService.formatDistance(distance);
  }

  List<Scooter> getScootersSortedByDistance() {
    if (_currentLocation == null) return _scooters;

    final scootersWithDistance = _scooters.map((scooter) {
      final distance = getDistanceToScooter(scooter);
      return MapEntry(scooter, distance ?? double.infinity);
    }).toList();

    scootersWithDistance.sort((a, b) => a.value.compareTo(b.value));
    return scootersWithDistance.map((entry) => entry.key).toList();
  }

  List<Scooter> filterScooters({
    ScooterStatus? status,
    int? minBatteryLevel,
    double? maxDistance,
  }) {
    return _scooters.where((scooter) {
      // Filter by status
      if (status != null && scooter.status != status) {
        return false;
      }

      // Filter by battery level
      if (minBatteryLevel != null && scooter.batteryLevel < minBatteryLevel) {
        return false;
      }

      // Filter by distance
      if (maxDistance != null) {
        final distance = getDistanceToScooter(scooter);
        if (distance == null || distance > maxDistance) {
          return false;
        }
      }

      return true;
    }).toList();
  }

  void updateScooterStatus(int scooterId, ScooterStatus newStatus) {
    final index = _scooters.indexWhere((s) => s.id == scooterId);
    if (index != -1) {
      _scooters[index] = _scooters[index].copyWith(status: newStatus);
      notifyListeners();
    }
  }

  void updateCurrentLocation(Position position) {
    _currentLocation = position;
    notifyListeners();
  }
}
