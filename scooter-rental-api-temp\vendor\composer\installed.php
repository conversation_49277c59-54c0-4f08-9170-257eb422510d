<?php return array(
    'root' => array(
        'name' => '__root__',
        'pretty_version' => '1.0.0+no-version-set',
        'version' => '1.0.0.0',
        'reference' => null,
        'type' => 'library',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(),
        'dev' => true,
    ),
    'versions' => array(
        '__root__' => array(
            'pretty_version' => '1.0.0+no-version-set',
            'version' => '1.0.0.0',
            'reference' => null,
            'type' => 'library',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'brick/math' => array(
            'pretty_version' => '0.14.0',
            'version' => '0.14.0.0',
            'reference' => '113a8ee2656b882d4c3164fa31aa6e12cbb7aaa2',
            'type' => 'library',
            'install_path' => __DIR__ . '/../brick/math',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'carbonphp/carbon-doctrine-types' => array(
            'pretty_version' => '3.2.0',
            'version' => '3.2.0.0',
            'reference' => '18ba5ddfec8976260ead6e866180bd5d2f71aa1d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../carbonphp/carbon-doctrine-types',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'doctrine/inflector' => array(
            'pretty_version' => '2.1.0',
            'version' => '2.1.0.0',
            'reference' => '6d6c96277ea252fc1304627204c3d5e6e15faa3b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../doctrine/inflector',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'illuminate/bus' => array(
            'pretty_version' => 'v12.33.0',
            'version' => '*********',
            'reference' => 'd239bd36ddb5b5c0996018528cbfe043fc1b5fea',
            'type' => 'library',
            'install_path' => __DIR__ . '/../illuminate/bus',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'illuminate/collections' => array(
            'pretty_version' => 'v12.33.0',
            'version' => '*********',
            'reference' => 'd47aaf15c55dd1c252688fdc7adbee129bd2ff0b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../illuminate/collections',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'illuminate/conditionable' => array(
            'pretty_version' => 'v12.33.0',
            'version' => '*********',
            'reference' => 'ec677967c1f2faf90b8428919124d2184a4c9b49',
            'type' => 'library',
            'install_path' => __DIR__ . '/../illuminate/conditionable',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'illuminate/console' => array(
            'pretty_version' => 'v12.33.0',
            'version' => '*********',
            'reference' => 'd6ee351b0cf26c0b9160e8de3859e1053cb471b1',
            'type' => 'library',
            'install_path' => __DIR__ . '/../illuminate/console',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'illuminate/container' => array(
            'pretty_version' => 'v12.33.0',
            'version' => '*********',
            'reference' => 'd6eaa8afd48dbe16b6b3c412a87479cad67eeb12',
            'type' => 'library',
            'install_path' => __DIR__ . '/../illuminate/container',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'illuminate/contracts' => array(
            'pretty_version' => 'v12.33.0',
            'version' => '*********',
            'reference' => '0bdbf0cdb5dd5739b2c8e6caf881a4114399ab15',
            'type' => 'library',
            'install_path' => __DIR__ . '/../illuminate/contracts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'illuminate/database' => array(
            'pretty_version' => 'v12.33.0',
            'version' => '*********',
            'reference' => 'cce8bca069820c32b51abbd54f732956b872cc6d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../illuminate/database',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'illuminate/events' => array(
            'pretty_version' => 'v12.33.0',
            'version' => '*********',
            'reference' => 'ba94fc7c734864e1eba802e75930725fd8074fce',
            'type' => 'library',
            'install_path' => __DIR__ . '/../illuminate/events',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'illuminate/filesystem' => array(
            'pretty_version' => 'v12.33.0',
            'version' => '*********',
            'reference' => 'c7c3bbcd05c0836af89f1b49bf70c3170c011c13',
            'type' => 'library',
            'install_path' => __DIR__ . '/../illuminate/filesystem',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'illuminate/macroable' => array(
            'pretty_version' => 'v12.33.0',
            'version' => '*********',
            'reference' => 'e862e5648ee34004fa56046b746f490dfa86c613',
            'type' => 'library',
            'install_path' => __DIR__ . '/../illuminate/macroable',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'illuminate/pipeline' => array(
            'pretty_version' => 'v12.33.0',
            'version' => '*********',
            'reference' => 'b6a14c20d69a44bf0a6fba664a00d23ca71770ee',
            'type' => 'library',
            'install_path' => __DIR__ . '/../illuminate/pipeline',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'illuminate/support' => array(
            'pretty_version' => 'v12.33.0',
            'version' => '*********',
            'reference' => '21016aede3dbeed1fccd4478dfbd9f10114456ce',
            'type' => 'library',
            'install_path' => __DIR__ . '/../illuminate/support',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'illuminate/view' => array(
            'pretty_version' => 'v12.33.0',
            'version' => '*********',
            'reference' => '7dd37bf2c957a54e9e580a097035685c49c3b9ca',
            'type' => 'library',
            'install_path' => __DIR__ . '/../illuminate/view',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'laravel/prompts' => array(
            'pretty_version' => 'v0.3.7',
            'version' => '*******',
            'reference' => 'a1891d362714bc40c8d23b0b1d7090f022ea27cc',
            'type' => 'library',
            'install_path' => __DIR__ . '/../laravel/prompts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'laravel/sanctum' => array(
            'pretty_version' => 'v4.2.0',
            'version' => '4.2.0.0',
            'reference' => 'fd6df4f79f48a72992e8d29a9c0ee25422a0d677',
            'type' => 'library',
            'install_path' => __DIR__ . '/../laravel/sanctum',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'laravel/serializable-closure' => array(
            'pretty_version' => 'v2.0.5',
            'version' => '2.0.5.0',
            'reference' => '3832547db6e0e2f8bb03d4093857b378c66eceed',
            'type' => 'library',
            'install_path' => __DIR__ . '/../laravel/serializable-closure',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'nesbot/carbon' => array(
            'pretty_version' => '3.10.3',
            'version' => '3.10.3.0',
            'reference' => '8e3643dcd149ae0fe1d2ff4f2c8e4bbfad7c165f',
            'type' => 'library',
            'install_path' => __DIR__ . '/../nesbot/carbon',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'nunomaduro/termwind' => array(
            'pretty_version' => 'v2.3.1',
            'version' => '2.3.1.0',
            'reference' => 'dfa08f390e509967a15c22493dc0bac5733d9123',
            'type' => 'library',
            'install_path' => __DIR__ . '/../nunomaduro/termwind',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/clock' => array(
            'pretty_version' => '1.0.0',
            'version' => '1.0.0.0',
            'reference' => 'e41a24703d4560fd0acb709162f73b8adfc3aa0d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/clock',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/clock-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/container' => array(
            'pretty_version' => '2.0.2',
            'version' => '2.0.2.0',
            'reference' => 'c71ecc56dfe541dbd90c5360474fbc405f8d5963',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/container',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/container-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.1|2.0',
            ),
        ),
        'psr/log-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0|2.0|3.0',
            ),
        ),
        'psr/simple-cache' => array(
            'pretty_version' => '3.0.0',
            'version' => '3.0.0.0',
            'reference' => '764e0b3939f5ca87cb904f570ef9be2d78a07865',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/simple-cache',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'spatie/once' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '*',
            ),
        ),
        'symfony/clock' => array(
            'pretty_version' => 'v7.3.0',
            'version' => '7.3.0.0',
            'reference' => 'b81435fbd6648ea425d1ee96a2d8e68f4ceacd24',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/clock',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/console' => array(
            'pretty_version' => 'v7.3.4',
            'version' => '7.3.4.0',
            'reference' => '2b9c5fafbac0399a20a2e82429e2bd735dcfb7db',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/console',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/deprecation-contracts' => array(
            'pretty_version' => 'v3.6.0',
            'version' => '3.6.0.0',
            'reference' => '63afe740e99a13ba87ec199bb07bbdee937a5b62',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/deprecation-contracts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/finder' => array(
            'pretty_version' => 'v7.3.2',
            'version' => '7.3.2.0',
            'reference' => '2a6614966ba1074fa93dae0bc804227422df4dfe',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/finder',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-ctype' => array(
            'pretty_version' => 'v1.33.0',
            'version' => '1.33.0.0',
            'reference' => 'a3cc8b044a6ea513310cbd48ef7333b384945638',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-ctype',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-intl-grapheme' => array(
            'pretty_version' => 'v1.33.0',
            'version' => '1.33.0.0',
            'reference' => '380872130d3a5dd3ace2f4010d95125fde5d5c70',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-intl-grapheme',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-intl-normalizer' => array(
            'pretty_version' => 'v1.33.0',
            'version' => '1.33.0.0',
            'reference' => '3833d7255cc303546435cb650316bff708a1c75c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-intl-normalizer',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-mbstring' => array(
            'pretty_version' => 'v1.33.0',
            'version' => '1.33.0.0',
            'reference' => '6d857f4d76bd4b343eac26d6b539585d2bc56493',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-mbstring',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-php83' => array(
            'pretty_version' => 'v1.33.0',
            'version' => '1.33.0.0',
            'reference' => '17f6f9a6b1735c0f163024d959f700cfbc5155e5',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-php83',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-php84' => array(
            'pretty_version' => 'v1.33.0',
            'version' => '1.33.0.0',
            'reference' => 'd8ced4d875142b6a7426000426b8abc631d6b191',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-php84',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-php85' => array(
            'pretty_version' => 'v1.33.0',
            'version' => '1.33.0.0',
            'reference' => 'd4e5fcd4ab3d998ab16c0db48e6cbb9a01993f91',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-php85',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/process' => array(
            'pretty_version' => 'v7.3.4',
            'version' => '7.3.4.0',
            'reference' => 'f24f8f316367b30810810d4eb30c543d7003ff3b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/process',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/service-contracts' => array(
            'pretty_version' => 'v3.6.0',
            'version' => '3.6.0.0',
            'reference' => 'f021b05a130d35510bd6b25fe9053c2a8a15d5d4',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/service-contracts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/string' => array(
            'pretty_version' => 'v7.3.4',
            'version' => '7.3.4.0',
            'reference' => 'f96476035142921000338bad71e5247fbc138872',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/string',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/translation' => array(
            'pretty_version' => 'v7.3.4',
            'version' => '7.3.4.0',
            'reference' => 'ec25870502d0c7072d086e8ffba1420c85965174',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/translation',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/translation-contracts' => array(
            'pretty_version' => 'v3.6.0',
            'version' => '3.6.0.0',
            'reference' => 'df210c7a2573f1913b2d17cc95f90f53a73d8f7d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/translation-contracts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/translation-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '2.3|3.0',
            ),
        ),
        'voku/portable-ascii' => array(
            'pretty_version' => '2.0.3',
            'version' => '2.0.3.0',
            'reference' => 'b1d923f88091c6bf09699efcd7c8a1b1bfd7351d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../voku/portable-ascii',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
    ),
);
