<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\ScooterController;
use App\Http\Controllers\RideController;
use App\Http\Controllers\AdminController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// Public routes (no authentication required)
Route::prefix('auth')->group(function () {
    Route::post('/register', [AuthController::class, 'register']);
    Route::post('/login', [AuthController::class, 'login']);
});

// Public scooter routes (for viewing available scooters)
Route::get('/scooters', [ScooterController::class, 'index']);
Route::get('/scooters/{scooter}', [ScooterController::class, 'show']);
Route::get('/scooters/code/{code}', [ScooterController::class, 'getByCode']);

// Protected routes (authentication required)
Route::middleware('auth:sanctum')->group(function () {
    
    // Authentication routes
    Route::prefix('auth')->group(function () {
        Route::post('/logout', [AuthController::class, 'logout']);
        Route::get('/profile', [AuthController::class, 'profile']);
        Route::put('/profile', [AuthController::class, 'updateProfile']);
        Route::delete('/account', [AuthController::class, 'deleteAccount']);
    });

    // User profile route (alternative)
    Route::get('/profile', [AuthController::class, 'profile']);

    // Ride routes
    Route::prefix('rides')->group(function () {
        Route::get('/', [RideController::class, 'index']); // Get ride history
        Route::post('/', [RideController::class, 'store']); // Start a ride
        Route::get('/current', [RideController::class, 'current']); // Get current active ride
        Route::get('/statistics', [RideController::class, 'statistics']); // Get user ride statistics
        Route::get('/{ride}', [RideController::class, 'show']); // Get specific ride
        Route::put('/{ride}/end', [RideController::class, 'end']); // End a ride
        Route::put('/{ride}/cancel', [RideController::class, 'cancel']); // Cancel a ride
    });

    // Scooter management routes (for maintenance/admin)
    Route::prefix('scooters')->group(function () {
        Route::get('/statistics', [ScooterController::class, 'statistics']);
        Route::put('/{scooter}/location', [ScooterController::class, 'updateLocation']);
        Route::put('/{scooter}/status', [ScooterController::class, 'updateStatus']);
        Route::put('/{scooter}/battery', [ScooterController::class, 'updateBattery']);
    });

    // Admin routes (require admin role - implement role middleware as needed)
    Route::prefix('admin')->middleware('admin')->group(function () {
        Route::get('/dashboard', [AdminController::class, 'dashboard']);
        Route::get('/users', [AdminController::class, 'users']);
        Route::get('/users/{user}', [AdminController::class, 'showUser']);
        Route::get('/scooters', [AdminController::class, 'scooters']);
        Route::post('/scooters', [AdminController::class, 'createScooter']);
        Route::put('/scooters/{scooter}', [AdminController::class, 'updateScooter']);
        Route::delete('/scooters/{scooter}', [AdminController::class, 'deleteScooter']);
        Route::get('/rides', [AdminController::class, 'rides']);
        Route::get('/rides/{ride}', [AdminController::class, 'showRide']);
        Route::get('/analytics', [AdminController::class, 'analytics']);
    });
});

// Health check route
Route::get('/health', function () {
    return response()->json([
        'status' => 'ok',
        'timestamp' => now()->toISOString(),
        'version' => '1.0.0',
    ]);
});

// API version info
Route::get('/version', function () {
    return response()->json([
        'api_version' => '1.0.0',
        'laravel_version' => app()->version(),
        'php_version' => PHP_VERSION,
    ]);
});

// Fallback route for undefined API endpoints
Route::fallback(function () {
    return response()->json([
        'message' => 'API endpoint not found',
    ], 404);
});
